import re
from collections import deque
from enum import Enum
from typing import List, Tuple

class BufferType(Enum):
    SENSITIVE = "sensitive"
    CORNER = "corner"
    CONTENT = "content"

class Buffer:
    def __init__(self, content: str, type_: BufferType):
        self.content = content
        self.type_ = type_
    
    def __repr__(self):
        return f"Buffer(content='{self.content}', type_={self.type_.value})"

class SimplifiedQABuffer:
    """简化版本的QABuffer，专注于citation匹配"""
    
    corner_keyword_pattern = re.compile(r'\[\s*citation\s*:\s*(\d+(?:\s*,\s*\d+)*)\s*\]')
    
    def __init__(self, keywords: List[str] = None):
        self.buffer_deque = deque()
        self.keywords = set(keywords) if keywords else set()
    
    async def generator(self, stream):
        async for char in stream:
            self.buffer_deque.append(char)
            buffer_text = "".join(self.buffer_deque)
            
            # 检查敏感词
            for keyword in self.keywords:
                if keyword in buffer_text:
                    yield Buffer(content=keyword, type_=BufferType.SENSITIVE)
                    return
            
            # 检查citation模式匹配
            if matches := self.find_corner_marks(buffer_text):
                start, end, refer_ids = matches[0]
                
                # 输出citation之前的内容
                if start > 0:
                    prefix = buffer_text[:start]
                    for c in prefix:
                        yield Buffer(content=c, type_=BufferType.CONTENT)
                
                # 输出citation引用ID
                for ref_id in refer_ids:
                    yield Buffer(content=ref_id, type_=BufferType.CORNER)
                
                # 清理已处理的内容，保留未处理的部分
                remaining = buffer_text[end:]
                self.buffer_deque.clear()
                for c in remaining:
                    self.buffer_deque.append(c)
                continue
            
            # 防止缓冲区无限增长
            if len(self.buffer_deque) > 20 and not self._has_potential_citation(buffer_text):
                yield Buffer(content=self.buffer_deque.popleft(), type_=BufferType.CONTENT)
        
        # 处理剩余内容
        while self.buffer_deque:
            yield Buffer(content=self.buffer_deque.popleft(), type_=BufferType.CONTENT)
    
    def _has_potential_citation(self, text: str) -> bool:
        """检查文本是否包含潜在的citation模式开始"""
        return '[' in text and ('citation' in text or 'c' in text)
    
    @classmethod
    def find_corner_marks(cls, text: str) -> List[Tuple[int, int, List[str]]]:
        """查找citation标记"""
        if not text:
            return []
        
        matches = []
        for match in cls.corner_keyword_pattern.finditer(text):
            start, end = match.span()
            refs = match.group(1)
            ref_ids = [num.strip() for num in refs.split(',') if num.strip().isdigit()]
            matches.append((start, end, ref_ids))
        
        return matches

async def test_stream(text: str):
    """模拟字符流"""
    for char in text:
        yield char

async def test_citation_buffer():
    """测试citation缓冲处理"""
    test_cases = [
        "普通文本[citation:1]后续内容",
        "多个引用[citation:1,2,3]测试",
        "带空格[ citation: 1 , 2 ]格式",
        "普通文本没有citation",
    ]
    
    buffer = SimplifiedQABuffer(keywords=["敏感词"])
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {test_text}")
        print("输出:")
        
        async for result in buffer.generator(test_stream(test_text)):
            print(f"  {result}")
        
        # 重置缓冲区
        buffer.buffer_deque.clear()

if __name__ == "__main__":
    import asyncio
    asyncio.run(test_citation_buffer())
