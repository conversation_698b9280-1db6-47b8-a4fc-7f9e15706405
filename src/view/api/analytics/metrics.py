#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from typing import Annotated

from fastapi import Query
from pydantic import Field

from view import BaseView, api_description
from controller.analytics import Dashboard, Metrics


class DashboardMetricsView(BaseView):
    @api_description(summary="获取分析指标")
    async def get(self,
                  dashboard_id: Annotated[int, Query(), Field(title="分析面板ID")],
                  item_id: Annotated[int, Query(), Field(title="数据项ID")] = None,
                  depth: Annotated[int, Query(), Field(title="数据深度")] = 100):
        dashboard = await Dashboard.get_one(dashboard_id=dashboard_id)
        # 所有深度内的看板任务
        tasks = await Metrics.get_task_all(dashboard_id=dashboard_id, depth=depth, order_by="id:asc")
        task_ids = [t["task_id"] for t in tasks]
        time_series = [t["data_time"] for t in tasks]

        # 所有看板数据项
        dashboard_items = await Dashboard.get_item_all(dashboard_id=dashboard_id, item_id=item_id)
        item_id_mapping = {it["item_id"]: it for it in dashboard_items}

        # 所有看板指标
        dashboard_metrics = await Dashboard.get_metric_all(dashboard_id=dashboard_id)

        # 深度内所有运算结果
        metrics_data = await Metrics.get_metrics_data(
            dashboard_id=dashboard_id,
            task_ids=[t["task_id"] for t in tasks],
            item_ids=[it["item_id"] for it in dashboard_items],
            desc=False)

        ydata = []
        for item_data in metrics_data:
            item_id = item_data["item_id"]
            if item_id not in item_id_mapping:
                continue
            item_data["item_name"] = item_id_mapping[item_id]["name"]

            metrics = item_data.pop("metrics")
            item_metrics = {
                dm["metric_id"]: {
                    "metric_id": dm["metric_id"],
                    "metric_name": dm["name"],
                    "metric_type": dm["type_"],
                    "values": [],
                    "explanations": []} for dm in dashboard_metrics
            }

            # 用current_task_id_idx来对齐数据task_id的指针,ES取出的数据有缺失导致时间序列对不齐
            current_task_id_idx = 0
            for task_metrics in metrics:
                # 记录所有当次任务插入的metric_ids,以保证当ES数据缺失整个任务或缺失某指标时,为所有指标补None
                insert_metric_ids = []

                # 如果任务维度对齐,则逐个插入指标
                if task_metrics["task_id"] == task_ids[current_task_id_idx]:
                    for m in task_metrics["extract_metrics"]:
                        if m["metric_id"] in item_metrics and m["metric_id"] not in insert_metric_ids:
                            item_metrics[m["metric_id"]]["values"].append(m["value"])
                            item_metrics[m["metric_id"]]["explanations"].append(m.get("explanation"))
                            insert_metric_ids.append(m["metric_id"])  # 记录插入的指标ID

                    # 指针索引自增
                    current_task_id_idx += 1

                # 不论丢失整个任务或丢失某指标,检查已插入的指标ID,未插入的指标ID补值
                for metric_id in item_metrics:
                    if metric_id not in insert_metric_ids:
                        item_metrics[metric_id]["values"].append(None)
                        item_metrics[metric_id]["explanations"].append(None)

            item_data["metrics"] = list(item_metrics.values())
            ydata.append(item_data)

        return self.response(data={
            "time_series": time_series,
            "data_series": ydata,
        })
