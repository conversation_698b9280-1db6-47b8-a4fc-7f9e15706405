#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import hashlib
import asyncio
from typing import Annotated, List
from pathlib import Path
from urllib.parse import quote

from fastapi import Query, Body, Form, File, UploadFile
from fastapi.responses import StreamingResponse
from pydantic import Field

from config import PARSER_FILE_SIZE
from engine.rdb import g
from engine.file_system import minio_client, repository_bucket
from view import BaseView, api_description
from exception import ParamsCheckError, NotFoundError, AccessDeniedError
from controller.repository import Repo, Doc, SupportFormat, DocStatus, ReferenceType
from exception import AlreadyExistsError, PermissionDenyError
from common.logger import logger


class DocListView(BaseView):
    @api_description(summary="查询文档列表")
    async def get(self,
                  repo_id: Annotated[int, Query(title="知识库ID")],
                  match: Annotated[str, Query(title="模糊匹配")] = None,
                  page: Annotated[int, Query(title="分页页数")] = None,
                  per_page: Annotated[int, Query(title="分页容量")] = None,
                  order_by: Annotated[str, Query(title="排序条件")] = "create_time:desc"):
        user_id = None if self.is_tenant_admin else self.user_id
        pager, docs = await Doc.get_list(
            repo_id=repo_id, user_id=user_id, match=match, page=page, per_page=per_page, order_by=order_by)

        for doc in docs:
            doc.setdefault("reference_type", ReferenceType.html)


        return self.response(data=docs, pager=pager)

class DocView(BaseView):
    @api_description(summary="查询文档详情")
    async def get(self,
                  doc_id: Annotated[int, Query(title="文档ID")]):
        user_id = None if self.is_tenant_admin else self.user_id
        if not (doc := await Doc.get_one(doc_id=doc_id, user_id=user_id)):
            raise NotFoundError("未找到目标数据")

        es_doc = await Doc.get_es_one(
            repo_id=doc["repo_id"],
            doc_id=doc_id,
            excludes=["html", "chunks", "title", "filename", "plain_text", *doc.keys()])
        doc.update(es_doc)

        # 如果非pdf溯源,返回html
        if not es_doc.get("reference_type") == ReferenceType.pdf:
            es_doc = await Doc.get_es_one(repo_id=doc["repo_id"], doc_id=doc_id, includes=["html"])
            doc.update(es_doc)

        return self.response(data=doc)

    @api_description(summary="删除文档")
    async def delete(self,
                     repo_id: Annotated[int, Body(title="知识库ID")],
                     doc_id: Annotated[int, Body(title="文档ID")]):
        user_id = None if self.is_tenant_admin else self.user_id

        repo = await Repo.get_one(repo_id=repo_id, user_id=user_id)
        if not repo["edit"]:
            raise PermissionDenyError("非创建人或超管无法修改该知识库内文档")
        if not (doc := await Doc.get_one(repo_id=repo_id, doc_id=doc_id, user_id=user_id)):
            raise NotFoundError("未找到目标数据")

        await Doc.delete(repo_id=repo_id, doc_id=doc_id)
        await g.session.commit()

        return self.response(message="删除成功")


class DocUploadView(BaseView):
    @api_description(summary="上传文档")
    async def post(self,
                   repo_id: Annotated[int, Form(title="文档库ID", description="文档库中上传时使用")] = 0,
                   files: List[UploadFile] = File(title="文件列表", default=None)):
        user_id = None if self.is_tenant_admin else self.user_id
        repo = await Repo.get_one(repo_id=repo_id, user_id=user_id)
        if not repo:
            raise NotFoundError("未找到目标数据")

        # 预处理和验证所有文件
        process_docs = []
        for file in files:
            name = file.filename
            suffix = Path(file.filename).suffix.lower()
            size = file.size
            try:
                if file.size > PARSER_FILE_SIZE * 1024 * 1024:  # 30MB
                    raise ParamsCheckError(message=f"文件大小超过限制: {PARSER_FILE_SIZE}MB")
                if suffix not in SupportFormat.__members__.values():
                    raise ParamsCheckError(message=f"不支持文件类型: {suffix}")

                file.file.seek(0)
                content = await file.read()
                md5 = hashlib.sha256(content).hexdigest()

                # 创建文档记录
                try:
                    doc_id = await Doc.create(repo_id=repo_id, name=name, md5=md5, size=size, status=DocStatus.uploading)
                except AlreadyExistsError:
                    raise ParamsCheckError(message="文件已存在")
                path = Doc.get_path(repo_id=repo_id, doc_id=doc_id, suffix=suffix.lower())
            except ParamsCheckError as e:
                logger.error(f"文档上传失败: {name} - {e.message}")
                process_docs.append({
                    "name": name,
                    "upload_status": 1,  # 业务失败,不可上传此文件
                    "error_message": e.message
                })
            else:
                process_docs.append({
                    "name": name,
                    "doc_id": doc_id,
                    "upload_status": 0,  # 成功
                    "content": content,
                    "path": path,
                    "error_message": None
                })

        # 批量上传文件
        upload_tasks = [(doc["doc_id"], doc["path"], doc.pop("content")) for doc in process_docs if doc["upload_status"] == 0]
        if upload_tasks:
            upload_results = await Doc.upload_s3_batch(upload_tasks)
            path_result = {doc_id: res for doc_id, res in upload_results}
        else:
            path_result = {}

        # 任意文件上传错误,都将使得API整体报错
        for doc in process_docs:
            if doc["upload_status"] == 0:
                if not path_result.get(doc["doc_id"]):
                    doc["upload_status"] = 2
                    doc["error_message"] = "网络失败或S3服务响应失败"
                    await Doc.delete(repo_id=repo_id, doc_id=doc["doc_id"])
                else:
                    await Doc.update(doc_id=doc["doc_id"], path=doc["path"], status=DocStatus.parsing)
                del doc["path"]

        await g.session.commit()

        for doc in process_docs:
            if doc["upload_status"] == 0:
                await Doc.send_doc_parsing_task(doc_id=doc["doc_id"])

        return self.response(data=process_docs)


class DocParsingRetryView(BaseView):
    @api_description(summary="解析文档")
    async def post(self,
                   doc_id: Annotated[int, Body(embed=True), Field(title="文档ID")]):
        if not (doc := await Doc.get_one(doc_id=doc_id, user_id=self.user_id)):
            raise NotFoundError("未找到目标数据")

        await Doc.update(doc_id=doc_id, status=DocStatus.parsing)
        await g.session.commit()
        await Doc.send_doc_parsing_task(doc_id=doc_id)

        return self.response(message="已开始重试解析")


class DocFileView(BaseView):
    @api_description(summary="获取文档文件")
    async def get(self,
                  doc_id: Annotated[int, Query(title="文档ID")],
                  format: Annotated[str, Query(title="文件格式")]):
        user_id = None if self.is_tenant_admin else self.user_id
        if not (doc := await Doc.get_one(doc_id=doc_id, user_id=user_id)):
            raise NotFoundError("未找到目标数据")

        filepath = Path(doc["path"])
        if format == "pdf":
            filepath = filepath.with_suffix(".pdf")

        bufsize = 1024 * 1024  # 1MB 缓冲区大小

        async def file_iterator():
            def _get_object():
                return minio_client.get_object(
                    bucket_name=repository_bucket,
                    object_name=filepath.as_posix()
                )

            obj = await asyncio.to_thread(_get_object)
            while True:
                chunk = obj.read(bufsize)
                if not chunk:
                    break
                yield chunk
            obj.close()

        return StreamingResponse(
            file_iterator(),
            headers={
                "Content-Disposition": f"attachment; filename*=UTF-8''{quote(filepath.name)}"
            }
        )


class DocAccessView(BaseView):
    @api_description(summary="检查文档是否可访问")
    async def get(self,
                  doc_id: Annotated[int, Query(title="文档ID")]):
        user_id = None if self.is_tenant_admin else self.user_id
        doc = await Doc.get_one(doc_id=doc_id, user_id=user_id)
        if doc and doc["tenant_id"] == g.tenant_id:
            return self.response(data={"access": True, "repo_name": doc["repo_name"]})

        return self.response(data={"access": False})
