from typing import Annotated

from fastapi import Query, Body

from common import g
from view import BaseView, api_description
from controller.chat.session import Session, ChatSessionType
from controller.chat.deep_research import DeepResearchStatus, DeepResearch


class ResearchSessionAllView(BaseView):
    @api_description(summary="查询全部")
    async def get(self,
                  session_id: Annotated[int, Query(title="会话ID")] = None,
                  match: Annotated[str, Query(title="模糊匹配")] = None):
        sessions = await Session.deep_research_get_all(
            session_id=session_id, match=match, user_id=g.user_id, session_type=ChatSessionType.DEEP_RESEARCH)
        return self.response(data=sessions)


class ResearchSessionView(BaseView):
    @api_description(summary="查询会话")
    async def get(self,
                  session_id: Annotated[int, Query(title="会话ID")]):
        session_item = await Session.get_one(session_id=session_id, session_type=ChatSessionType.DEEP_RESEARCH)
        message_item = await DeepResearch.get_message(session_id=session_id)
        return self.response(data={**session_item.model_dump(), **message_item.model_dump()})

    @api_description(summary="创建会话")
    async def post(self):
        session_id = await Session.create(ChatSessionType.DEEP_RESEARCH)
        await DeepResearch.create_message(session_id=session_id, status=DeepResearchStatus.pending)
        await g.session.commit()
        return self.response(data={"session_id": session_id})

    @api_description(summary="修改会话")
    async def put(self,
                  session_id: Annotated[int, Body(title="会话ID")],
                  session_title: Annotated[str, Body(title="会话标题")] = None):
        await Session.update(session_id=session_id, title=session_title, session_type=ChatSessionType.DEEP_RESEARCH)
        await g.session.commit()
        return self.response(message="修改成功")

    @api_description(summary="删除会话")
    async def delete(self,
                     session_id: Annotated[int, Body(embed=True, title="Session ID")]):
        await Session.delete(session_id=session_id, session_type=ChatSessionType.DEEP_RESEARCH)
        await g.session.commit()
        return self.response(message="删除成功")


class ResearchStopView(BaseView):
   @api_description(summary="停止深入研究")
   async def post(self,
                  session_id: Annotated[int, Body(title="会话ID", embed=True)]):
       await Session.stop_streaming(session_id=session_id)
       return self.response(message="停止成功")
