#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from typing import List, Dict, Any
from elasticsearch.exceptions import NotFoundError

from common.time import now_datetime_str
from engine.es import es
from model.deep_research_es import DEEP_RESEARCH_INDEX
from controller.operator.workflow.deep_research import DeepResearchResponse
from common.logger import logger


class DeepResearchESService:
    """DeepResearch结果的ES持久化服务"""

    @staticmethod
    async def save_research_result(
        session_id: int,
        user_id: int,
        tenant_id: int,
        response: DeepResearchResponse,
        sequence: int,
        total_results: int,
    ):
        """保存DeepResearch结果到ES"""

        # 构建ES文档
        doc = {
            "session_id": session_id,
            "user_id": user_id,
            "tenant_id": tenant_id,
            "response": response.model_dump(),
            "error_msg": response.error_msg,
            "create_time": now_datetime_str(),
            "sequence": sequence,
            "total_results": total_results
        }

        try:
            # 使用session_id和sequence作为文档ID，确保唯一性
            doc_id = f"{session_id}_{sequence}"
            result = await es.index(
                index=DEEP_RESEARCH_INDEX,
                id=doc_id,
                body=doc
            )
            logger.info(f"Saved DeepResearch result to ES: session_id={session_id}, sequence={sequence}")
            return result
        except Exception as e:
            logger.error(f"Error saving DeepResearch result to ES: {e}")
            raise

    @staticmethod
    async def get_research_results(
        session_id: int,
        size: int = 2000,
        from_: int = 0
    ) -> List[Dict[str, Any]]:
        """获取指定会话的DeepResearch结果"""
        try:
            query = {
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"session_id": session_id}}
                        ]
                    }
                },
                "sort": [
                    {"sequence": {"order": "asc"}},
                ],
                "size": size,
                "from": from_
            }

            result = await es.search(
                index=DEEP_RESEARCH_INDEX,
                body=query
            )

            return [hit["_source"] for hit in result["hits"]["hits"]]
        except NotFoundError:
            logger.warning(f"DeepResearch index not found: {DEEP_RESEARCH_INDEX}")
            return []
        except Exception as e:
            logger.error(f"Error getting DeepResearch results from ES: {e}")
            raise

    @staticmethod
    async def search_research_results(
        session_ids: List[int],
        size: int = 50,
        from_: int = 0
    ) -> Dict[str, Any]:
        """搜索DeepResearch结果"""
        try:
            must_conditions = [{"terms": {"session_id": session_ids}}]

            # 构建搜索查询
            search_query = {
                "query": {
                    "bool": {
                        "must": must_conditions,
                        # "should": [
                        #     {"match": {"user_prompt": {"query": query_text, "boost": 3}}},
                        #     {"match": {"title": {"query": query_text, "boost": 2}}},
                        #     {"match": {"content": {"query": query_text, "boost": 1}}},
                        #     {"match": {"model_plan": {"query": query_text, "boost": 1.5}}},
                        #     {"match": {"user_plan": {"query": query_text, "boost": 1.5}}}
                        # ],
                        # "minimum_should_match": 1
                    }
                },
                "sort": [
                    {"_score": {"order": "desc"}},
                    {"create_time": {"order": "desc"}}
                ],
                "size": size,
                "from": from_
            }

            result = await es.search(
                index=DEEP_RESEARCH_INDEX,
                body=search_query
            )

            return {
                "total": result["hits"]["total"]["value"],
                "results": [
                    {
                        **hit["_source"]
                    }
                    for hit in result["hits"]["hits"]
                ]
            }
        except NotFoundError:
            logger.warning(f"DeepResearch index not found: {DEEP_RESEARCH_INDEX}")
            return {"total": 0, "results": []}
        except Exception as e:
            logger.error(f"Error searching DeepResearch results: {e}")
            raise

    @staticmethod
    async def delete_session_results(session_id: int, user_id: int, tenant_id: int):
        """删除指定会话的所有DeepResearch结果"""
        try:
            query = {
                "query": {
                    "bool": {
                        "must": [
                            {"term": {"session_id": session_id}},
                            {"term": {"user_id": user_id}},
                            {"term": {"tenant_id": tenant_id}}
                        ]
                    }
                }
            }

            result = await es.delete_by_query(
                index=DEEP_RESEARCH_INDEX,
                body=query
            )

            logger.info(f"Deleted DeepResearch results for session {session_id}: {result['deleted']} documents")
            return result
        except NotFoundError:
            logger.warning(f"DeepResearch index not found: {DEEP_RESEARCH_INDEX}")
            return {"deleted": 0}
        except Exception as e:
            logger.error(f"Error deleting DeepResearch results: {e}")
            raise


if __name__ == '__main__':
    import asyncio

    async def test():
        ans = await DeepResearchESService.get_research_results(session_id=837)
        print(ans)

    asyncio.run(test())
