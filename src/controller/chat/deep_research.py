import asyncio
import json
import time
import traceback

from sqlalchemy import select, update
from loguru import logger as logging

from controller.chat.session import Session
from controller.operator.runner.qa.generate_title import GenerateTitle
from controller.operator.workflow.deep_research import DeepResearchPlanWorkflow, DeepResearchStage, \
    DeepResearchSearchWorkflow, DeepResearchResponse
from controller.chat.deep_research_es import DeepResearchESService
from engine.cache import r_cache
from engine.rdb import query_order, fetch_one
from exception import NotFoundError
from model.session import DeepResearchStatus, DeepResearchMessageModel, DeepResearchMessage
from common.time import now_tz_datestring_with_millis
from common import g


class DeepResearchController:
    async def get_stream(self, session_id: int):
        index, stop_flag = f"deep_research:{session_id}", "1"

        try:
            message = await self.get_message(session_id=session_id)
            if not message:
                yield f"data: {DeepResearchResponse(stage=DeepResearchStage.ERROR, content='未找到会话信息').model_dump_json()}\n\n"
                raise NotFoundError

            yield f"data: {message.model_dump_json()}\n\n"

            if message.status == DeepResearchStatus.planning:
                for _ in range(10 ** 4):
                    message = await self.get_message(session_id=session_id)
                    if message.status == DeepResearchStatus.planed or message.status == DeepResearchStatus.plan_rejected:
                        yield f"data: {message.model_dump_json()}\n\n"
                        break
                    else:
                        await asyncio.sleep(1)
                        await g.session.commit()

            elif message.status == DeepResearchStatus.researching:
                # 实时读取缓存中的研究进度数据
                last_length = 0
                for _ in range(10 ** 5):  # 设置最大循环次数防止无限循环
                    # todo 后续需要修复停止生成带来的问题
                    # stop_flag = await Session.check_streaming(session_id=session_id)
                    # if not stop_flag:
                    #     await self.update_message(session_id=session_id, status=DeepResearchStatus.stop)
                    #     yield f"data: {DeepResearchResponse(stage=DeepResearchStage.STOP).model_dump_json()}\n\n"
                    #     break

                    # 获取当前缓存列表长度
                    current_length = await r_cache.llen(index)
                    print(current_length)

                    # 如果有新数据，读取并发送
                    if current_length > last_length:
                        # 获取新增的数据
                        new_items = await r_cache.lrange(index, last_length, current_length - 1)
                        for item in new_items:
                            yield item
                        last_length = current_length

                    # 检查研究状态是否已完成
                    message = await self.get_message(session_id=session_id)
                    print(message)
                    if message.status == DeepResearchStatus.success:
                        # 确保发送所有剩余的缓存数据
                        remaining_items = await r_cache.lrange(index, last_length, -1)
                        for item in remaining_items:
                            yield item

                        break
                    elif message.status == DeepResearchStatus.error:
                        # 发送错误信息
                        error_content = f'研究过程出错: {message.error_msg or "未知错误"}'
                        error_response = DeepResearchResponse(stage=DeepResearchStage.ERROR, content=error_content)
                        yield f"data: {error_response.model_dump_json()}\n\n"

                        break

                    await asyncio.sleep(1)  # 短暂等待避免过于频繁的查询
                    await g.session.commit()

            elif message.status == DeepResearchStatus.success or message.status == DeepResearchStatus.stop:
                result_items = await DeepResearchESService.get_research_results(session_id=session_id)
                for item in result_items:
                    yield f"data: {json.dumps(item.get('response'), ensure_ascii=False)}\n\n"

            elif message.status == DeepResearchStatus.error:
                error_content = f'研究过程出错: {message.error_msg or "未知错误"}'
                error_response = DeepResearchResponse(stage=DeepResearchStage.ERROR, content=error_content)
                yield f"data: {error_response.model_dump_json()}\n\n"

            else:
                yield f"data: {DeepResearchResponse(stage=DeepResearchStage.ERROR, content='未实现的会话状态').model_dump_json()}\n\n"

        except Exception as e:
            error_msg = ''.join(traceback.format_exception(type(e), value=e, tb=e.__traceback__))
            await self.update_message(session_id=session_id, status=DeepResearchStatus.error, error_msg=error_msg)
            yield f"data: {DeepResearchResponse(stage=DeepResearchStage.ERROR, content=error_msg).model_dump_json()}\n\n"

        finally:
            await g.session.close()
            yield "data: [DONE]\n\n"

    async def make_plan(self, session_id: int, user: str):
        try:
            await Session.start_streaming(session_id=session_id)
            asyncio.create_task(self._generate_title(session_id=session_id, user=user))
            workflow = DeepResearchPlanWorkflow(user_prompt=user)

            async for item in workflow.run_stream():
                stop_flag = await Session.check_streaming(session_id=session_id)
                if not stop_flag:
                    await self.update_message(session_id=session_id, status=DeepResearchStatus.stop)
                    yield f"data: {DeepResearchResponse(stage=DeepResearchStage.STOP).model_dump_json()}\n\n"
                    break

                if item.stage == DeepResearchStage.ANSWER:
                    await self.update_message(session_id=session_id, status=DeepResearchStatus.plan_rejected,
                                              model_plan=item.content)
                else:
                    item.content = item.content.replace("\n\n", "\n").strip()
                    await self.update_message(session_id=session_id, status=DeepResearchStatus.planed,
                                              model_plan=item.content)

                yield f"data: {item.model_dump_json()}\n\n"

        except Exception as e:
            error_msg = ''.join(traceback.format_exception(type(e), value=e, tb=e.__traceback__))
            await self.update_message(session_id=session_id, status=DeepResearchStatus.error, error_msg=error_msg)

        finally:
            await g.session.commit()
            await g.session.close()
            await Session.stop_streaming(session_id=session_id)
            yield "data: [DONE]\n\n"

    async def execute_research(self, session_id: int, user: str, plan: str):
        index, stop_flag = f"deep_research:{session_id}", "1"
        research_results = []

        try:
            await Session.start_streaming(session_id=session_id)
            workflow = DeepResearchSearchWorkflow(user_prompt=user, plan=plan)

            async for item in workflow.run_stream():
                stop_flag = await Session.check_streaming(session_id=session_id)
                if not stop_flag:
                    workflow.stop_flag = True
                    await self.update_message(session_id=session_id, status=DeepResearchStatus.stop,
                                              model_plan=item.content)
                    yield f"data: {DeepResearchResponse(stage=DeepResearchStage.STOP).model_dump_json()}\n\n"
                    break

                # 存储到Redis缓存（用于实时流式输出）
                await r_cache.rpush(index, f"data: {item.model_dump_json()}\n\n")
                yield f"data: {item.model_dump_json()}\n\n"

                # 收集结果用于ES持久化存储
                research_results.append(item)

            if stop_flag:
                await self.update_message(session_id=session_id, status=DeepResearchStatus.success)

        except Exception as e:
            error_msg = ''.join(traceback.format_exception(type(e), value=e, tb=e.__traceback__))
            yield f"data: {DeepResearchResponse(stage=DeepResearchStage.ERROR, content=error_msg).model_dump_json()}\n\n"
            await self.update_message(session_id=session_id, status=DeepResearchStatus.error, error_msg=error_msg)

        finally:
            # 研究完成后，将结果持久化到ES
            await self.save_results_to_es(
                session_id=session_id,
                research_results=research_results
            )
            await g.session.commit()
            await g.session.close()
            await Session.stop_streaming(session_id=session_id)
            yield "data: [DONE]\n\n"

    @staticmethod
    async def get_query(session_id: int, status: DeepResearchStatus = None, order_by: str = "update_time:desc"):
        where = []
        if session_id is not None:
            where.append(DeepResearchMessageModel.session_id == session_id)
        if status is not None:
            where.append(DeepResearchMessageModel.status == status)

        query = (
            select(
                DeepResearchMessageModel.session_id,
                DeepResearchMessageModel.status,
                DeepResearchMessageModel.user,
                DeepResearchMessageModel.model_plan,
                DeepResearchMessageModel.user_plan,
                DeepResearchMessageModel.plan_start_time,
                DeepResearchMessageModel.plan_end_time,
                DeepResearchMessageModel.research_start_time,
                DeepResearchMessageModel.research_end_time,
                DeepResearchMessageModel.error_msg,
            ).where(*where))
        query = query_order(query=query, table=DeepResearchMessageModel, order_by=order_by)

        return query

    async def get_message(self, session_id: int, status: DeepResearchStatus = None) -> DeepResearchMessage | None:
        query = await self.get_query(session_id=session_id, status=status)
        message = await fetch_one(query)

        if not message:
            return None

        return DeepResearchMessage(**message)

    @staticmethod
    async def create_message(session_id: int, status: DeepResearchStatus = DeepResearchStatus.pending):
        session = DeepResearchMessageModel(
            session_id=session_id,
            status=status,
            create_time=now_tz_datestring_with_millis(),
        )
        g.session.add(session)
        await g.session.flush()

        return session.id

    async def update_message(self, session_id: int, status: DeepResearchStatus = None, user: str = None,
                             model_plan: str = None, user_plan: str = None, error_msg: str = None):
        query = await self.get_query(session_id=session_id)
        message = await fetch_one(query)

        if message is None:
            raise NotFoundError("未找到指定数据")

        update_info = {}
        if status is not None:
            update_info[DeepResearchMessageModel.status] = status
        if user is not None:
            update_info[DeepResearchMessageModel.user] = user
            update_info[DeepResearchMessageModel.plan_start_time] = now_tz_datestring_with_millis()
        if model_plan is not None:
            update_info[DeepResearchMessageModel.model_plan] = model_plan
            update_info[DeepResearchMessageModel.plan_end_time] = now_tz_datestring_with_millis()
        if user_plan is not None:
            update_info[DeepResearchMessageModel.user_plan] = user_plan
            update_info[DeepResearchMessageModel.research_start_time] = now_tz_datestring_with_millis()
        if error_msg is not None:
            update_info[DeepResearchMessageModel.error_msg] = error_msg

        query = (
            update(DeepResearchMessageModel)
            .where(DeepResearchMessageModel.session_id == session_id)
            .values(update_info)
        )
        await g.session.execute(query)

    @staticmethod
    async def _generate_title(session_id: int, user: str):
        s = time.time()
        runner = GenerateTitle(chat_history=[{"role": "user", "content": user}],)
        title = await runner.run()
        await Session.update(session_id=session_id, title=title)
        await g.session.commit()
        logging.info(
            f"{session_id=} 未检测到会话标题，通过意图识别获取到的标题: {title}, 用时:{time.time() - s:.4f}")

    async def save_results_to_es(self, session_id: int, research_results: list):
        """将DeepResearch结果保存到ES中进行持久化存储"""
        try:
            # 获取会话和消息信息
            message = await self.get_message(session_id=session_id)
            if not message:
                logging.warning(f"Session {session_id} message not found, skipping ES save")
                return

            total_results = len(research_results)

            # 批量保存所有研究结果到ES
            for sequence, response in enumerate(research_results):
                try:
                    await DeepResearchESService.save_research_result(
                        session_id=session_id,
                        user_id=g.user_id,
                        tenant_id=g.tenant_id,
                        response=response,
                        sequence=sequence,
                        total_results=total_results,
                    )
                except Exception as e:
                    logging.error(f"Failed to save research result {sequence} to ES: {e}")
                    # 继续保存其他结果，不因单个失败而中断
                    continue

            logging.info(f"Successfully saved {total_results} DeepResearch results to ES for session {session_id}")

        except Exception as e:
            logging.error(f"Error saving DeepResearch results to ES for session {session_id}: {e}")


DeepResearch = DeepResearchController()
