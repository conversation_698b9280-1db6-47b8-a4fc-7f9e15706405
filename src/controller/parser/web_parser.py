#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import hashlib
import asyncio
import chardet

import httpx
from controller.parser.chunker.tokenizer import BGE_M3_TOKENIZER
from elasticsearch import AsyncElasticsearch

from config import ES_URL
from common.logger import logger
from common.time import now_tz_datestring
from controller.parser.chunker import HtmlChunker
from controller.repository import DocStatus
from controller.parser.base import ParserBase
from model.doc import WEB_DOC_INDEX

# 这里需要单独构建一个ES,避免其retry参数
es = AsyncElasticsearch(ES_URL, request_timeout=60, max_retries=0)


class WebParser(ParserBase):
    def __init__(self, doc_id: int, url: str, title: str, content: str, source: str = None, data_time: str = None,
                 icon: str = None):
        super().__init__(
            doc_id=doc_id,
            chunker=HtmlChunker(tokenizer=BGE_M3_TOKENIZER),
            embedding_engine=None,
            embedding_batch_size=0)
        self.url = url
        self.title = title
        self.content = content
        self.source = source
        self.data_time = data_time
        self.icon = icon

        self.index = WEB_DOC_INDEX

        self.log_prefix = f"Doc[{self.doc_id}]网页解析 "
        self.http_timeout = httpx.Timeout(6)

        # 默认分片字段权重
        self.chunk_field_boost = {
            "chunks.title": 2,
            "chunks.plain_content": 1,
        }

    async def exec(self):
        """
        爬取目标网页,进行html分块解析,最后存入ES
        Returns:

        """
        try:
            html = await self.crawl()
            if not html:
                raise ValueError("未找到html文本内容")
        except Exception as err:
            logger.error(self.log_prefix + f"爬取失败: {self.url} {str(err)}")
            html = f"""<!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <title>{self.title}</title>
            </head>
            <body>
                <p>{self.content}</p>
            </body>
            </html>"""

        self.chunks = self.chunker.chunk(html_content=html)
        self.html = html
        await self.merge_plain_text()
        try:
            await self.es_data_index()
        except Exception as err:
            logger.error(f"{self.log_prefix}ES上传失败: {str(err)}")

    async def crawl(self):
        """
        爬取目标网页内容
        Returns:

        """
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Upgrade-Insecure-Requests": "1",
            "Sec-Fetch-Dest": "document",
            "Sec-Fetch-Mode": "navigate",
            "Sec-Fetch-Site": "none",
            "Cache-Control": "max-age=0"
        }
        if not self.url:
            return None
        async with httpx.AsyncClient(
                transport=httpx.AsyncHTTPTransport(),
                timeout=self.http_timeout
        ) as client:
            response = await client.get(
                url=self.url,
                headers=headers,
                follow_redirects=True)
            # 使用 response.text 自动处理编码，或者手动检测编码
            if response.encoding is None:
                # 如果没有检测到编码，尝试从内容中检测
                detected = chardet.detect(response.content)
                response.encoding = detected["encoding"] or "utf-8"
            return response.text

    async def es_data_index(self):
        logger.info(f"{self.log_prefix}执行ES文档构建上传")
        chunks = [
            {
                "cid": f"{self.doc_id}_{i}",
                "index": i,
                "title": chunk.title,
                "html_content": "".join([node.html_content for node in chunk.nodes]),
                "origin_plain_content": chunk.origin_plain_content,
                "plain_content": chunk.plain_content,
                "xpath": [node.xpath for node in chunk.nodes if node.xpath],
                "token_counts": chunk.token_counts,
                "start_offset": chunk.start_offset,
                "end_offset": chunk.end_offset
            }
            for i, chunk in enumerate(self.chunks)
        ]

        es_doc = {
            "doc_id": self.doc_id,
            "tenant_id": None,
            "repo_id": None,
            "filename": self.title,
            "status": DocStatus.parse_success if self.error is None else DocStatus.parse_fail,
            "tags": [],
            "title": self.title,
            "source": self.source,
            "icon": self.icon,
            "url": self.url,
            "author": None,
            # 减小上传IO
            # "html": self.html,
            # "plain_text": self.plain_text,
            "token_counts": sum([chunk.token_counts for _, chunk in enumerate(self.chunks) if chunk.token_counts]),
            "chunks": chunks,
            "keywords": [],
            "data_time": self.data_time,
            "create_time": now_tz_datestring(),
        }
        await es.index(
            index=self.index,
            id=str(self.doc_id),
            document=es_doc,
            timeout="10s",
            error_trace=True,
            refresh=True
        )


if __name__ == '__main__':
    parser = WebParser(doc_id=123121312321321321321,
                       url="https://www.sohu.com/a/879738272_122066678",
                       title="中石化与宁德时代重磅合作：计划建万座换电站，新能源未来在何方",
                       content="本理财产品的《投资协议书》、《销售（代理销售）协议书》、《产品说明书》、. 《风险揭示书》、《投资者权益须知》等文件及其不时有效修订与补充共同")
    asyncio.run(parser.exec())
