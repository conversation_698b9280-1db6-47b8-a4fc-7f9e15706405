from abc import abstractmethod
from typing import Annotated, List, Optional

from lxml import html as lxmlhtml

from pydantic import BaseModel, Field, model_validator


class ExtractModel(BaseModel):
    subject: Annotated[List[str], Field(title="主体")] = []
    industry: Annotated[List[str], Field(title="行业")] = []
    positive_view: Annotated[List[str], Field(title="正面观点")] = []
    negative_view: Annotated[List[str], Field(title="负面观点")] = []
    sentiment_score: Annotated[Optional[float], Field(title="情绪因子")] = None
    keywords: Annotated[List[str], Field(title="关键信息")] = []
    abstract: Annotated[str, Field(title="摘要")] = None
    analysis: Annotated[Optional[str], Field(title="主观分析")] = None


class ChunkModel(BaseModel):
    index: Annotated[Optional[int], Field(title="索引")] = None
    chunk_text: Annotated[str, Field(title="分块文本")]
    start_offset: Annotated[int, Field(title="起始偏移量")]
    end_offset: Annotated[int, Field(title="结束偏移量")]
    extract_result: Annotated[Optional[ExtractModel], Field(title="提取结果")] = None


class RetrieveChunkModel(BaseModel):
    """retrieve chunk结构"""
    cid: Annotated[Optional[str], Field(title="分片唯一ID")]
    filename: Annotated[Optional[str], Field(title="文件名")]
    data_time: Annotated[Optional[str], Field(title="数据时间", description="对于上传数据可能无意义")] = None
    doc_id: Annotated[int, Field(title="文档ID")]
    xpath: Annotated[List[str], Field(title="xpath表达式")]
    html_content: Annotated[str, Field(title="html格式文本内容")] = None
    token_counts: Annotated[int, Field(title="token数量(默认BGE-M3)")] = None
    web_search: Annotated[bool, Field(title="是否来自网络抓取")] = None
    url: Annotated[Optional[str], Field(title="文档链接")] = None
    icon: Annotated[Optional[str], Field(title="icon图标")] = None
    type_: Annotated[Optional[str], Field(title="文档块类型")] = None
    bboxes: Annotated[Optional[List[dict]], Field(title="bbox信息")] = None
    reference_type: Annotated[Optional[str], Field(title="溯源类型")] = None

    # 非必传参数,默认不输出
    title: Annotated[List[str], Field(exclude=True, title="分片标题")] = None
    plain_content: Annotated[str, Field(exclude=True, title="纯文本内容")] = None
    score: Annotated[Optional[float], Field(exclude=True, title="相关性评分")] = None
    judge_text: Annotated[Optional[str], Field(exclude=True, title="临时参数,用于判断排序的文本")] = None
    relevance_score: Annotated[Optional[float], Field(exclude=True, title="临时参数,相关性评分")] = None

    class Config:
        use_enum_values = True

    def __hash__(self):
        """基于唯一标识字段计算哈希值，用于set去重
        
        使用以下字段组合作为唯一标识：
        1. cid: 分片唯一ID（如果存在）
        2. doc_id + xpath: 文档ID和路径组合（作为备选唯一标识）
        """
        if self.cid:
            return hash(self.cid)
        else:
            # 将xpath列表转换为元组以便哈希
            xpath_tuple = tuple(self.xpath) if self.xpath else ()
            return hash((self.doc_id, xpath_tuple))
    
    def __eq__(self, other):
        """判断两个RetrieveChunkModel是否相等
        
        基于相同的唯一标识逻辑：
        - 如果两个对象都有cid，比较cid
        - 否则比较doc_id和xpath的组合
        """
        if not isinstance(other, RetrieveChunkModel):
            return False
        
        # 如果两个都有cid，直接比较cid
        if self.cid and other.cid:
            return self.cid == other.cid
        
        # 否则比较doc_id和xpath
        return self.doc_id == other.doc_id and self.xpath == other.xpath

    def llm_output(self):
        """过滤给大模型的信息,当前主要过滤img标签"""
        if "<img src=" in self.html_content and "base64" in self.html_content:
            html_tree = lxmlhtml.fromstring(self.html_content)
            for img in html_tree.xpath('.//img'):
                img.getparent().remove(img)
            html_content = lxmlhtml.tostring(html_tree, encoding="unicode", pretty_print=False)

            return {
                "html_content": html_content,
                **self.model_dump(include={"filename", "data_time"})
            }

        return self.model_dump(include={"filename", "html_content", "data_time"})



class DocModel(BaseModel):
    title: Annotated[Optional[str], Field(title="标题")] = None
    content: Annotated[str, Field(title="完整文本")]
    data_time: Annotated[Optional[str], Field(title="发布时间")] = None
    chunk_children: Annotated[List[ChunkModel], Field(title="分块文本列表")] = []
    extract_result: Annotated[Optional[ExtractModel], Field(title="提取结果汇总")] = None

    @model_validator(mode="after")
    def replace_image_tag(self):
        if "<img src=" in self.content and "base64" in self.content:
            try:
                html_tree = lxmlhtml.fromstring(self.content)
                for img in html_tree.xpath('.//img'):
                    img.getparent().remove(img)
                self.content = lxmlhtml.tostring(html_tree, encoding="unicode", pretty_print=False)
            except Exception as e:
                pass

        return self


class CombineModel(BaseModel):
    layer: Annotated[int, Field(title="层级")] = 1
    chunk_node: Annotated[ChunkModel, Field(title="分块文本模型")]
    children_node: Annotated[Optional[List["CombineModel"]], Field(title="子节点")] = []


class BaseChunking:
    @abstractmethod
    def chunk(self, *args, **kwargs) -> DocModel:
        raise NotImplementedError
