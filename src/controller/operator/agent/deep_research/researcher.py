import asyncio
import json
from datetime import datetime
from enum import StrEnum
from typing import Sequence, AsyncIterable, Optional, Annotated, Any, List, Dict

from autogen_agentchat.base import Response
from autogen_agentchat.messages import TextMessage, BaseChatMessage
from autogen_core import CancellationToken
from autogen_core.models import ChatCompletionClient
from pydantic import BaseModel, Field

from common import g
from controller.engine import deepseek_v3_1_0821, deepseek_v3_1_0821_thinking
from controller.operator.agent import BaseAgent
from controller.operator.runner.base import TokenConsumption
from controller.operator.tool.retriever import repo_search, web_search
from controller.operator.tool.time import get_current_time
from controller.operator.prompt.deep_research.researcher import researcher_prompt, researcher_summary_prompt, \
    researcher_reflect_strict_prompt


class ResearchStage(StrEnum):
    """研究阶段枚举"""
    QUERY_GENERATION = "query_generation"  # 生成查询关键词
    SEARCH_RESULT = "search_result"  # 搜索结果
    SUMMARY = "summary"  # 摘要结果
    REFLECTION = "reflection"  # 反思结果
    COMPLETED = "completed"  # 研究完成
    ERROR = "error"  # 错误


class ResearchOutput(BaseModel):
    """研究输出数据模型"""
    stage: Annotated[ResearchStage, Field(title="研究阶段")]
    iteration: Annotated[int, Field(title="迭代轮次", ge=1, le=5)]
    create_time: Annotated[datetime, Field(title="创建时间", default_factory=datetime.now)] = None
    query_list: Annotated[Optional[List[str]], Field(title="查询关键词列表")] = None
    search_results: Annotated[Optional[Dict[str, Dict[str, Any]]], Field(title="搜索结果")] = None
    summary: Annotated[Optional[str], Field(title="摘要内容")] = None
    reflection: Annotated[Optional[str], Field(title="反思评估")] = None
    is_approved: Annotated[bool, Field(title="是否通过评估")] = False
    history_summary: Annotated[Optional[str], Field(title="累积历史摘要")] = None
    error_msg: Annotated[Optional[str], Field(title="错误信息")] = None


class ResearchAgent(BaseAgent):
    def __init__(self, model_client: ChatCompletionClient = deepseek_v3_1_0821, token_consumption=None):
        self.token_consumption = token_consumption or TokenConsumption()

        super().__init__(
            name="research",
            model_client=model_client,
            token_consumption=token_consumption,
            system_message=researcher_prompt.format(
                current_time=get_current_time()
            ),
        )


class ResearchSummaryAgent(BaseAgent):
    def __init__(self, model_client: ChatCompletionClient = deepseek_v3_1_0821, token_consumption=None):
        self.token_consumption = token_consumption or TokenConsumption()

        super().__init__(
            name="research_summary",
            model_client=model_client,
            token_consumption=token_consumption,
            system_message=researcher_summary_prompt.format(
                current_time=get_current_time(),
            ),
        )


class ResearcherReflectStrictAgent(BaseAgent):
    def __init__(self, model_client: ChatCompletionClient = deepseek_v3_1_0821, token_consumption=None):
        self.token_consumption = token_consumption or TokenConsumption()

        super().__init__(
            name="researcher_reflect",
            model_client=model_client,
            token_consumption=token_consumption,
            system_message=researcher_reflect_strict_prompt.format(
                current_time=get_current_time(),
            ),
        )


def get_query_list(response: Response):
    """
    从大模型响应中提取查询关键词列表
    处理各种可能的返回格式和错误情况
    """
    try:
        msg = response.chat_message.content
        if not msg:
            return []
        
        # 清理消息内容
        msg = msg.strip()
        
        # 尝试提取JSON块（处理被markdown代码块包裹的情况）
        # 支持 ```json 或 ``` 格式
        if "```json" in msg:
            start = msg.find("```json") + 7
            end = msg.find("```", start)
            if end != -1:
                msg = msg[start:end].strip()
        elif "```" in msg:
            # 处理没有指定语言的代码块
            start = msg.find("```") + 3
            end = msg.find("```", start)
            if end != -1:
                potential_json = msg[start:end].strip()
                # 检查是否像JSON
                if potential_json.startswith("{") or potential_json.startswith("["):
                    msg = potential_json
        
        # 尝试直接解析JSON
        try:
            data = json.loads(msg)
        except json.JSONDecodeError:
            # 如果解析失败，尝试查找JSON对象
            # 查找第一个{到最后一个}之间的内容
            start_idx = msg.find("{")
            end_idx = msg.rfind("}")
            if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                json_str = msg[start_idx:end_idx + 1]
                try:
                    data = json.loads(json_str)
                except json.JSONDecodeError:
                    # 仍然失败，返回空列表
                    return []
            else:
                return []
        
        # 从解析后的数据中提取query_keywords_list
        if isinstance(data, dict):
            query_list = data.get("query_keywords_list", [])
            # 确保返回的是列表
            if isinstance(query_list, list):
                # 过滤掉非字符串和空字符串
                return [q for q in query_list if isinstance(q, str) and q.strip()]
            else:
                return []
        elif isinstance(data, list):
            # 如果直接返回了列表，假设它就是查询列表
            return [q for q in data if isinstance(q, str) and q.strip()]
        else:
            return []
            
    except Exception as e:
        # 记录错误但不中断流程
        print(f"解析查询列表时出错: {e}")
        return []


async def research_teams(sub_plan: str) -> AsyncIterable[ResearchOutput]:
    """改造后的research_teams函数，返回异步生成器
    
    Args:
        sub_plan: 研究计划
        
    Yields:
        ResearchOutput: 研究过程中的各阶段输出
    """
    cancellation_token = CancellationToken()
    
    # 维护历史摘要，用于累积每轮查询的结果
    history_summary = ""
    max_iterations = 3
    iter_num = 0
    
    for iter_num in range(max_iterations):
        researcher_agent = ResearchAgent()
        researcher_summary_agent = ResearchSummaryAgent()
        researcher_reflect_agent = ResearcherReflectStrictAgent()

        # Step 1: 生成查询请求
        # 如果有历史摘要，将其作为上下文提供
        if history_summary:
            content = f"研究计划: {sub_plan}\n\n已有研究摘要:\n{history_summary}"
        else:
            content = f"研究计划: {sub_plan}"
            
        communicate_messages: Sequence[BaseChatMessage] = [
            TextMessage(source="user", content=content),
        ]

        response = await researcher_agent.on_messages(messages=communicate_messages,
                                                      cancellation_token=cancellation_token)
        query_list = get_query_list(response)
        
        if not query_list:
            continue
            
        # Yield查询生成结果
        yield ResearchOutput(
            stage=ResearchStage.QUERY_GENERATION,
            iteration=iter_num + 1,
            query_list=query_list,
            history_summary=history_summary if history_summary else None
        )

        # Step 2: 执行查询
        # 创建并发任务列表 - 对每个query同时执行repo_search和web_search
        if len(query_list) == 1:
            topn = 10
        elif len(query_list) <= 3:
            topn = 5
        else:
            topn = 3

        search_tasks = []
        for query in query_list:
            search_tasks.append(repo_search(query, topn=topn, user_id=g.user_id))
            search_tasks.append(web_search(query, topn=topn))

        # 并发执行所有搜索任务
        try:
            search_results = await asyncio.gather(*search_tasks, return_exceptions=True)
        except Exception as e:
            # Yield错误状态
            yield ResearchOutput(
                stage=ResearchStage.ERROR,
                iteration=iter_num + 1,
                error_msg=f"DeepResearch 搜索任务执行失败: {e}"
            )
            raise Exception(f"DeepResearch 搜索任务执行失败: {e}")

        query_result = {}
        for i, query in enumerate(query_list):
            query_result[query] = {
                "repo_search": search_results[2 * i],
                "web_search": search_results[2 * i + 1],
            }
        
        # Yield搜索结果
        yield ResearchOutput(
            stage=ResearchStage.SEARCH_RESULT,
            iteration=iter_num + 1,
            search_results=query_result,
            query_list=query_list
        )
            
        # Step 3: 使用ResearchSummaryAgent总结当前轮次的查询结果
        # 格式化查询结果为摘要agent可理解的格式
        search_content = format_search_results_for_summary(query_result, query_list)
        
        summary_messages: Sequence[BaseChatMessage] = [
            TextMessage(source="user", content=f"""研究计划: {sub_plan}

当前轮次搜索结果:
{search_content}

请总结上述搜索结果的核心要点。"""),
        ]
        
        summary_response = await researcher_summary_agent.on_messages(
            messages=summary_messages,
            cancellation_token=cancellation_token
        )
        
        # 获取当前轮次的摘要
        current_round_summary = summary_response.chat_message.content
        
        # Yield摘要结果
        yield ResearchOutput(
            stage=ResearchStage.SUMMARY,
            iteration=iter_num + 1,
            summary=current_round_summary
        )
        
        # 累积历史摘要
        if history_summary:
            history_summary = f"{history_summary}\n\n--- 第{iter_num + 1}轮研究 ---\n{current_round_summary}"
        else:
            history_summary = f"--- 第{iter_num + 1}轮研究 ---\n{current_round_summary}"
        
        # Step 4: 使用反思agent根据所有历史摘要评估是否需要继续研究
        reflect_messages: Sequence[BaseChatMessage] = [
            TextMessage(source="user", content=f"""研究计划: {sub_plan}

累积研究摘要:
{history_summary}

请评估上述所有研究摘要是否充分满足研究计划的要求。"""),
        ]
        
        reflect_response = await researcher_reflect_agent.on_messages(
            messages=reflect_messages,
            cancellation_token=cancellation_token
        )
        
        reflect_content = reflect_response.chat_message.content
        
        # 检查是否通过评估
        is_approved = "APPROVE" in reflect_content
        
        # Yield反思结果
        yield ResearchOutput(
            stage=ResearchStage.REFLECTION,
            iteration=iter_num + 1,
            reflection=reflect_content,
            is_approved=is_approved,
            history_summary=history_summary
        )
    
    # Yield最终完成状态
    yield ResearchOutput(
        stage=ResearchStage.COMPLETED,
        iteration=iter_num + 1 if iter_num < max_iterations else max_iterations,
        history_summary=history_summary,
        is_approved=True
    )


def format_search_results_for_summary(query_result: dict, query_list: list) -> str:
    """将查询结果格式化为摘要agent可理解的文本格式，仅提取plain_content字段以节省上下文"""
    formatted_content = []
    
    for query in query_list:
        formatted_content.append(f"\n### 查询关键词: {query}")
        
        # 格式化知识库搜索结果
        repo_results = query_result[query].get("repo_search", [])
        if repo_results and not isinstance(repo_results, Exception):
            formatted_content.append("\n#### 知识库搜索结果:")
            if isinstance(repo_results, list):
                for idx, result in enumerate(repo_results[:5], 1):  # 只取前5个最相关结果
                    # 处理RetrieveChunkModel对象，提取plain_content或html_content
                    if hasattr(result, 'plain_content'):
                        # 优先使用plain_content纯文本内容
                        content = result.plain_content if result.plain_content else ""
                        if not content and hasattr(result, 'html_content'):
                            # 如果plain_content为空，使用html_content作为备选
                            content = extract_text_from_html(result.html_content)
                        if content:
                            # 添加来源信息
                            source_info = f"[来源: {result.filename}]" if hasattr(result, 'filename') and result.filename else ""
                            formatted_content.append(f"{idx}. {content} {source_info}")
                    elif isinstance(result, dict):
                        # 处理字典格式的结果
                        content = result.get('plain_content', '') or result.get('html_content', '')
                        if content and 'html_content' in result and 'plain_content' not in result:
                            content = extract_text_from_html(content)
                        if content:
                            source_info = f"[来源: {result.get('filename', '')}]" if result.get('filename') else ""
                            formatted_content.append(f"{idx}. {content} {source_info}")
                    else:
                        # 其他格式直接转字符串
                        formatted_content.append(f"{idx}. {str(result)}")
            else:
                formatted_content.append(str(repo_results))
        
        # 格式化网络搜索结果
        web_results = query_result[query].get("web_search", [])
        if web_results and not isinstance(web_results, Exception):
            formatted_content.append("\n#### 网络搜索结果:")
            if isinstance(web_results, list):
                for idx, result in enumerate(web_results[:5], 1):  # 只取前5个最相关结果
                    # 处理RetrieveChunkModel对象，提取plain_content或html_content
                    if hasattr(result, 'plain_content'):
                        # 优先使用plain_content纯文本内容
                        content = result.plain_content if result.plain_content else ""
                        if not content and hasattr(result, 'html_content'):
                            # 如果plain_content为空，使用html_content作为备选
                            content = extract_text_from_html(result.html_content)
                        if content:
                            # 添加来源信息
                            source_info = f"[来源: {result.url}]" if hasattr(result, 'url') and result.url else ""
                            formatted_content.append(f"{idx}. {content} {source_info}")
                    elif isinstance(result, dict):
                        # 处理字典格式的结果
                        content = result.get('plain_content', '') or result.get('html_content', '')
                        if content and 'html_content' in result and 'plain_content' not in result:
                            content = extract_text_from_html(content)
                        if content:
                            source_info = f"[来源: {result.get('url', '')}]" if result.get('url') else ""
                            formatted_content.append(f"{idx}. {content} {source_info}")
                    else:
                        # 其他格式直接转字符串
                        formatted_content.append(f"{idx}. {str(result)}")
            else:
                formatted_content.append(str(web_results))
    
    return "\n".join(formatted_content)


def extract_text_from_html(html_content: str) -> str:
    """从HTML内容中提取纯文本，移除HTML标签"""
    if not html_content:
        return ""
    
    try:
        from lxml import html as lxmlhtml
        # 解析HTML
        html_tree = lxmlhtml.fromstring(html_content)
        # 提取所有文本内容
        text_content = html_tree.text_content()
        # 清理多余的空白字符
        text_content = ' '.join(text_content.split())
        return text_content[:2000]  # 限制最大长度为2000字符
    except Exception:
        # 如果解析失败，尝试简单的标签移除
        import re
        text = re.sub('<[^<]+?>', '', html_content)
        text = ' '.join(text.split())
        return text[:2000]



if __name__ == '__main__':
    import asyncio

    async def test_research_teams():
        """测试改造后的research_teams函数"""
        sub_plan = "检索宁德时代官方年报、招股书及公司官网，提取基础信息：成立时间、创始人背景、注册地、股权结构等基础档案。"
        
        print(f"研究任务: {sub_plan}")
        print("=" * 80)
        
        async for output in research_teams(sub_plan):
            timestamp = output.create_time.strftime('%H:%M:%S')
            
            # 使用if-elif确保每次只处理一种状态
            if output.stage == ResearchStage.QUERY_GENERATION:
                print(f"\n[{timestamp}] 第{output.iteration}轮 - 查询关键词生成:")
                for i, query in enumerate(output.query_list, 1):
                    print(f"  {i}. {query}")
                    
            elif output.stage == ResearchStage.SEARCH_RESULT:
                print(f"\n[{timestamp}] 第{output.iteration}轮 - 搜索结果:")
                for query, results in output.search_results.items():
                    repo_results = results.get("repo_search", [])
                    web_results = results.get("web_search", [])
                    repo_count = len(repo_results) if not isinstance(repo_results, Exception) else 0
                    web_count = len(web_results) if not isinstance(web_results, Exception) else 0
                    print(f"  查询 '{query}':")
                    print(f"    - 知识库: {repo_count} 条结果")
                    print(f"    - 网络搜索: {web_count} 条结果")
                    
            elif output.stage == ResearchStage.SUMMARY:
                print(f"\n[{timestamp}] 第{output.iteration}轮 - 摘要:")
                print(f"  {output.summary}")
                
            elif output.stage == ResearchStage.REFLECTION:
                print(f"\n[{timestamp}] 第{output.iteration}轮 - 评估结果:")
                status = "✓ 通过评估" if output.is_approved else "○ 需要继续研究"
                print(f"  状态: {status}")
                # 显示反思内容预览
                if output.reflection:
                    print(f"  评估意见: {output.reflection}")
                    
            elif output.stage == ResearchStage.COMPLETED:
                print(f"\n[{timestamp}] ✓ 研究完成！")
                print("=" * 80)
                print("\n最终研究报告:")
                print("-" * 40)
                print(output.history_summary)
                print("-" * 40)
                break
                
            elif output.stage == ResearchStage.ERROR:
                print(f"\n[{timestamp}] ✗ 错误发生: {output.error_msg}")
                break
    
    asyncio.run(test_research_teams())
