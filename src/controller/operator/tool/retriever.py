from typing import List

from controller.operator.chunking import RetrieveChunkModel
from controller.retriever import HybridRetriever, WebSearchConfig, SearchEngine


async def repo_search(query: str, topn: int = 10, user_id: int = None) -> List[RetrieveChunkModel]:
    retriever = HybridRetriever(
        max_doc_size=topn * 2,
        topn=topn,
        doc_threshold=50,  # 关联度阈值,推荐深入研究设置的较高(50)
        # user_id=user_id
    )
    docs = await retriever.retrieve(query=query)
    return docs


async def web_search(query: str, topn: int = 10) -> List[RetrieveChunkModel]:
    retriever = HybridRetriever(
        web_search=WebSearchConfig(
            name=SearchEngine.zhipu,
            search_config={
                "extract": False,  # 是否进入URL进行html解析/切片/入库和召回
                "count": topn,  # 提取的网页数量，智谱最多支持Top50，但会过滤掉没有url的信息
                "allow_non_url": False  # 是否允许返回非URL结果
            }
        ),
        repo_search=False)  # 关闭知识库检索
    docs = await retriever.retrieve(query=query)
    return docs


if __name__ == "__main__":
    import asyncio

    # print("知识库搜索: ", asyncio.run(repo_search(query="隆基绿能在西北有什么项目")))
    print("网络搜索: ", asyncio.run(web_search(query="隆基绿能在西北有什么项目")))
