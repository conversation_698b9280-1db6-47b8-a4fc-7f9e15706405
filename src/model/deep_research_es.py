#!/usr/bin/env python3
# -*- coding: utf-8 -*-

DEEP_RESEARCH_INDEX = "deep_research_results"

DEEP_RESEARCH_MAPPING = {
    "settings": {
        "number_of_shards": 1,
        "number_of_replicas": 0,
        "analysis": {
            "filter": {
                "stop_words_filter": {
                    "type": "stop",
                    "ignore_case": True,
                    "stopwords_path": "/usr/share/elasticsearch/config/stopwords.txt"
                },
                "word_delimiter_graph_filter": {
                    "type": "word_delimiter_graph",
                    "adjust_offsets": False
                },
                "lowercase_filter": {
                    "type": "lowercase"
                },
            },
            "analyzer": {
                "ik_smart": {
                    "type": "custom",
                    "tokenizer": "ik_smart",
                    "filter": [
                        "word_delimiter_graph_filter",
                        "lowercase_filter",
                        "stop_words_filter",
                    ]
                },
                "ik_max_word": {
                    "type": "custom",
                    "tokenizer": "ik_max_word",
                    "filter": [
                        "lowercase_filter",
                        "word_delimiter_graph_filter",
                    ]
                },
            },
        }
    },
    "mappings": {
        "dynamic": False,
        "properties": {
            # 会话相关信息
            "session_id": {
                "type": "integer"
            },
            "user_id": {
                "type": "integer"
            },
            "tenant_id": {
                "type": "integer"
            },

            # 预留字段
            "content": {
                "type": "text",
                "analyzer": "ik_max_word",
                "search_analyzer": "ik_smart"
            },
            "response": {
                "type": "nested",
                "properties": {
                    "stage": {
                        "type": "keyword"
                    },
                    "create_time": {
                        "type": "date",
                        "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd HH:mm:ss.SSS||epoch_millis||strict_date_optional_time||yyyy-MM-dd'T'HH:mm:ss.SSSSSS"
                    },
                    "title": {
                        "type": "text",
                        "analyzer": "ik_max_word",
                        "search_analyzer": "ik_smart"
                    },
                    "content": {
                        "type": "text",
                        "analyzer": "ik_max_word",
                        "search_analyzer": "ik_smart"
                    },
                    "reference": {
                        "type": "object",
                        "enabled": True,
                        "dynamic": True,
                        "properties": {
                            "doc_id": {
                                "type": "keyword"
                            }
                        }
                    },
                    "error_msg": {
                        "type": "text",
                        "analyzer": "ik_max_word",
                        "search_analyzer": "ik_smart"
                    }
                }
            },
            "error_msg": {
                "type": "text",
                "analyzer": "ik_max_word",
                "search_analyzer": "ik_smart"
            },

            # 时间戳字段
            "create_time": {
                "type": "date",
                "format": "yyyy-MM-dd HH:mm:ss||yyyy-MM-dd HH:mm:ss.SSS||epoch_millis"
            },

            # 序列号，用于结果排序
            "sequence": {
                "type": "integer"
            },

            # 结果总数统计
            "total_results": {
                "type": "integer"
            }
        }
    }
}
