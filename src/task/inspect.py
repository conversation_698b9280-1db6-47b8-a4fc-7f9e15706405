#!/usr/bin/env python3
# -*- coding: utf-8 -*-
from engine.es import es
from engine.rdb import  g
from controller.repository import Doc
from common.logger import logger


class InspectOfflineTask:
    @staticmethod
    async def doc_inconsistency():
        logger.info("文档不一致巡检开始")

        es_res = await es.search(
            index="repo_*", size=10000, query={"match_all": {}}, source_includes=["doc_id", "repo_id"], sort="doc_id:desc")

        _, docs = await Doc.get_list(page=1, per_page=10000, order_by="id:desc")
        doc_id_mapping = [doc["doc_id"] for doc in docs]
        for hit in es_res["hits"]["hits"]:
            doc_id = hit["_source"]["doc_id"]
            repo_id = hit["_source"]["repo_id"]
            if doc_id not in doc_id_mapping:
                logger.warning(f"发现不一致文档: [{doc_id}] 进行删除操作")
                await Doc.delete(repo_id=repo_id, doc_id=doc_id)
                await g.session.commit()
                logger.info(f"删除不一致文档: [{doc_id}] 完成")
        logger.info("文档不一致巡检结束")


InspectOffline = InspectOfflineTask()


if __name__ == '__main__':
    import asyncio
    asyncio.run(InspectOffline.doc_inconsistency())