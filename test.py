import re

corner_keyword_pattern = re.compile(r'\[\s*citation\s*:\s*(\d+(?:\s*,\s*\d+)*)\s*\]')

text1 = "中国人工智能科技创新Top 50[citation:1]。\n\n2. **产品与技术**"
text2 = "aaabbbccc [citation:1,2]"
text3 = "aaabbbccc [citation: 1 , 2,3 ]"
text4 = "aaabbbccc [ citation: 1 ]"

# 测试所有情况
test_texts = [text1, text2, text3, text4]
for i, text in enumerate(test_texts, 1):
    matches = corner_keyword_pattern.findall(text)
    print(f"text{i}: {text}")
    print(f"匹配结果: {matches}")
    print(f"完整匹配: {corner_keyword_pattern.finditer(text)}")
    for match in corner_keyword_pattern.finditer(text):
        print(f"  完整匹配内容: '{match.group(0)}', 捕获组: '{match.group(1)}'")
    print("-" * 50)

