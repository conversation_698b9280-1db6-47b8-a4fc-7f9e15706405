import re
from typing import List, Tuple

class CitationMatcher:
    """简化的citation匹配器"""
    
    # 改进的正则表达式，支持各种空格格式
    corner_keyword_pattern = re.compile(r'\[\s*citation\s*:\s*(\d+(?:\s*,\s*\d+)*)\s*\]')
    
    @classmethod
    def find_corner_marks(cls, text: str) -> List[Tuple[int, int, List[str]]]:
        """
        在字符串中查找所有角标，并提取其中的数字。
        
        Args:
            text (str): 输入字符串。
            
        Returns:
            List[Tuple[int, int, List[str]]]: 包含所有角标的元组列表，每个元组包含 (起始位置, 结束位置, 引用ID列表)。
        """
        if not text:
            return []
        
        matches = []
        
        for match in cls.corner_keyword_pattern.finditer(text):
            start, end = match.span()
            refs = match.group(1)
            ref_ids = [num.strip() for num in refs.split(',') if num.strip().isdigit()]
            matches.append((start, end, ref_ids))
        
        return matches

def test_citation_formats():
    """测试各种citation格式"""
    test_cases = [
        "中国人工智能科技创新Top 50[citation:1]。",
        "这是一个测试 [citation:1,2] 文本",
        "支持空格格式 [citation: 1 , 2,3 ] 的测试",
        "更多空格 [ citation: 1 ] 的情况",
        "多个citation [citation:1] 和 [citation:2,3] 在同一段",
        "无效格式 [citation:] 应该被忽略",
        "正常文本没有citation标记",
    ]
    
    matcher = CitationMatcher()
    
    for i, text in enumerate(test_cases, 1):
        print(f"测试用例 {i}: {text}")
        matches = matcher.find_corner_marks(text)
        if matches:
            for start, end, ref_ids in matches:
                matched_text = text[start:end]
                print(f"  匹配: '{matched_text}' -> 引用ID: {ref_ids}")
        else:
            print("  无匹配")
        print("-" * 60)

if __name__ == "__main__":
    test_citation_formats()
